
import { color } from '@/constants/theme';
import { isWeb } from '@/utils/helpers';
import { Check, ChevronDown } from 'lucide-react-native';
import { useColorScheme } from 'nativewind';
import React from 'react';
import DropdownSelect from 'react-native-input-select';

interface ThemedDropdownSelectProps {
  label?: string;
  placeholder?: string;
  options?: any[];
  optionLabel?: string;
  optionValue?: string;
  selectedValue?: any;
  onValueChange?: (value: any) => void;
  primaryColor?: string;
  isSearchable?: boolean;
  isMultiple?: boolean;
  error?: string;
  [x: string]: any; // For other props
}

const ThemedDropdownSelect = ({
  label = '',
  placeholder = 'Select...',
  options = [],
  optionLabel = 'label',
  optionValue = 'value',
  selectedValue = '',
  onValueChange = () => {},
  primaryColor = 'green',
  isSearchable = false,
  isMultiple = false,
  error = undefined,
  ...props
}: ThemedDropdownSelectProps) => {
  const { colorScheme } = useColorScheme();
  const theme = colorScheme === 'dark' ? 'dark' : 'light';

  return (
    <DropdownSelect
      label={label}
      placeholder={placeholder}
      options={options}
      optionLabel={optionLabel}
      optionValue={optionValue}
      selectedValue={selectedValue}
      onValueChange={onValueChange}
      primaryColor={primaryColor}
      isSearchable={isSearchable}      
      isMultiple={isMultiple}
      dropdownErrorStyle={{
        borderColor: 'red',
        borderWidth: 2,
        borderStyle: 'solid',
      }}
      dropdownErrorTextStyle={{ color: 'red', fontWeight: '500' }}
      error={error || (selectedValue === undefined ? 'This field is required' : '')}      
      dropdownContainerStyle={{
        marginVertical: 12,  
        marginHorizontal: 0,            
      }}
      dropdownStyle={{
        paddingHorizontal: 12,
        paddingVertical: 16,
        // marginVertical: 12,
        backgroundColor: theme === 'dark' ? '#262626' : '#ffffff',
        borderColor: theme === 'dark' ? '#171717' : '#d1d5db',
        borderRadius: 12,
        minHeight: 52,
        // borderWidth: 0, // To remove border, set borderWidth to 0                
      }}      
      dropdownIcon={
        <ChevronDown
          size={20}
          color={theme === 'dark' ? color.dark.textColor : color.light.textColor}
        />
      }
      dropdownIconStyle={{
        top: isWeb ? 18 : 0,
        bottom: isWeb ? 18 : 0,
        right: isWeb ? 6 : 0,
        paddingLeft: 6,
        backgroundColor: theme === 'dark' ? color.dark.backgroundColor : color.light.backgroundColor,
      }}
      selectedItemStyle={{
        fontSize: 14,
        color: theme === 'dark' ? color.dark.textColor : color.light.textColor,
      }}
      listComponentStyles={{
        itemSeparatorStyle: {
          backgroundColor: theme === 'dark' ? '#e3e3e3' : '#262626',
          height: 1,
          opacity: 0.15,
        },
        listEmptyComponentStyle: {
          color: theme === 'dark' ? color.dark.textColor : color.light.textColor,
        },
        sectionHeaderStyle: {
          color: theme === 'dark' ? color.dark.textColor : color.light.textColor,
        },
      }}
      modalControls={{
        modalBackgroundStyle: {
          // 
        },
        modalOptionsContainerStyle: {
          backgroundColor: theme === 'dark' ? '#262626' : '#ffffff',
        }
      }}
      searchControls={{
        textInputStyle: {
          backgroundColor: theme === 'dark' ? '#262626' : '#ffffff',
          borderColor: theme === 'dark' ? '#aaa' : '#bbb',
          color: theme === 'dark' ? color.dark.textColor : color.light.textColor,
          borderRadius: 12,
          minHeight: 52,
          borderWidth: 1,
          borderStyle: 'solid',
        },
        textInputContainerStyle: {
          backgroundColor: 'transparent',
        },
        // searchCallback: (value: string) => console.log('Search value:', value),
      }}
      checkboxControls={{
        checkboxSize: 16,
        checkboxStyle: {
          backgroundColor: theme === 'dark' ? '#262626' : '#262626',
          borderColor: theme === 'dark' ? '#aaa' : '#bbb',
          borderWidth: 0,
        },
        checkboxLabelStyle: {
          color: theme === 'dark' ? color.dark.textColor : color.light.textColor,
        },
        checkboxUnselectedColor:  'white',
        checkboxComponent: selectedValue ? (
          <Check
            size={14}
            // color={theme === 'dark' ? color.dark.textColor : color.light.textColor}
            color='white'
          />
        ) : null,
      }}
    />
  );
};

export default ThemedDropdownSelect;