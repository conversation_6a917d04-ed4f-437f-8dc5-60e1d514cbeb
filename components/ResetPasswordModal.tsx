import { resetPassword } from '@/api/auth';
import { yupResolver } from '@hookform/resolvers/yup';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'sonner-native';
import * as yup from 'yup';
import BottomSheetModal from './BottomSheetModal';
import ErrorMessage from './ErrorMessage';
import FormInputErrorMessage from './FormInputErrorMessage';
import ThemedButton from './ThemedButton';
import ThemedTextInput from './ThemedTextInput';
import ThemedView from './ThemedView';

interface OtpVerificationSheetModalProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  email: string;
  verificationSignature: any;
  onDismiss?: () => void;
  onConfirm?: (data: any) => void;
}

const ResetPasswordModal = ({ visible, setVisible, email, verificationSignature, onDismiss, onConfirm }: OtpVerificationSheetModalProps) => {

  
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Define a validation schema
  const schema = yup.object({
    password: yup.string().required().label('New password'),
    password_confirmation: yup.string()
                              .required()
                              .label('Confirm new password')
                              .oneOf([yup.ref('password')], 'Password confirmation did not match'),
  });

  const { control, handleSubmit, formState: { errors }, reset, setError } = useForm({
    defaultValues: {
      password: "",
      password_confirmation: "",
    },
    resolver: yupResolver(schema),
  })

  const handleDismiss = () => {
    setVisible(false);
    onDismiss && onDismiss();
  };

  const onSubmit = (data: any) => {
    setIsLoading(true);
    setErrorMessage('');
    resetPassword(email, data.password, data.password_confirmation, verificationSignature).then((response) => {
      if (response.status === "success") {
        // check if verification token is required again
        if (response.data?.require_verification_token_signature) {
          handleDismiss();
          toast.error('Something went wrong. Please try again.');
        } else {
          // success
          // clear form
          reset();
          setIsLoading(false);
          onConfirm && onConfirm(response.data || null);
          toast.success(response.message, { duration: 10000 });
        }  
      } else {
        if (response.errors) {
          Object.keys(response.errors).forEach((key: any) => {
            setError(key, {
              type: 'custom',
              message: response.errors[key][0],
            });
          });
        }
        setErrorMessage(response.message);
        toast.error(response.message);
        setIsLoading(false);
      }
    });
  };

  return (
    <BottomSheetModal 
      visible={visible}
      setVisible={setVisible}
      snapPointIndex={0}
      snapPoints={["75%",  '90%']}
      onDismiss={handleDismiss}
      loading={isLoading}
      title="Create New Password"
    >
    {!verificationSignature || !email ? (
      <ErrorMessage>Something went wrong. Please try again.</ErrorMessage>
    ) : (
      <>
        <ThemedView className="">
          <Controller
            control={control}
            rules={{
              required: true,
              minLength: 6,
            }}
            render={({ field: { onChange, onBlur, value } }) => (
              <ThemedTextInput
                placeholder="New Password"
                secureTextEntry
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
                togglePasswordVisibility={true}
                useBottomSheetTextInput={true}
              />
              )}
            name="password"
          />
          {errors.password && <FormInputErrorMessage>{ errors.password.message }</FormInputErrorMessage>}

          <Controller
            control={control}
            rules={{
              required: true,
              minLength: 6,
            }}
            render={({ field: { onChange, onBlur, value } }) => (
              <ThemedTextInput
                placeholder="Confirm New Password"
                secureTextEntry
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
                togglePasswordVisibility={true}
                useBottomSheetTextInput={true}
              />
              )}
            name="password_confirmation"
          />
          {errors.password_confirmation && <FormInputErrorMessage>{ errors.password_confirmation.message }</FormInputErrorMessage>}
          
        </ThemedView>

        <ThemedButton onPress={handleSubmit(onSubmit)}>Reset Password</ThemedButton>
      </>
    )}
    </BottomSheetModal>
  );
};

export default ResetPasswordModal;
