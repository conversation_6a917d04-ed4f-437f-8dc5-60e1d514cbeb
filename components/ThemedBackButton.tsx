import { styles } from '@/constants/theme';
// import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { RelativePathString } from 'expo-router/build/types';
import { ChevronLeft } from 'lucide-react-native';
import { useColorScheme } from 'nativewind';
import React from 'react';
import { Platform, TouchableOpacity } from 'react-native';
import ThemedText from './ThemedText';
import ThemedView from './ThemedView';

interface ThemedBackButtonProps {
  defaultRoute?: RelativePathString | any;
}

const ThemedBackButton = ({ defaultRoute = '/' }: ThemedBackButtonProps) => {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const theme = colorScheme === 'dark' ? 'dark' : 'light';

  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace(defaultRoute || '/');
    }
  };

  return (
    <TouchableOpacity onPress={() => handleBack()} className="">
      <ThemedView className="flex-row items-center">
        {/* <Ionicons name="chevron-back" size={28} color={theme === 'dark' ? styles.dark.headerTint.color : styles.light.headerTint.color} /> */}
        <ChevronLeft size={28} color={theme === 'dark' ? styles.dark.headerTint.color : styles.light.headerTint.color} />
        {Platform.OS === 'ios' && (<ThemedText className="font-normal text-lg">Back</ThemedText>)}        
      </ThemedView>
    </TouchableOpacity>
  )
}

export default ThemedBackButton