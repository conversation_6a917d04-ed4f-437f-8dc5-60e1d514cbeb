import { Eye, EyeOff } from 'lucide-react-native';
import { useColorScheme } from 'nativewind';
import React, { useState } from 'react';
import { Dimensions, Text, TouchableOpacity } from 'react-native';
import ThemedLink from './ThemedLink';
import ThemedText from './ThemedText';
import ThemedView from './ThemedView';

const BalanceCard = ( { account }: { account: any }) => {
  const { colorScheme } = useColorScheme();
  const theme = colorScheme === 'dark' ? 'dark' : 'light';
  const width = Dimensions.get('window').width;

  const [showBalance, setShowBalance] = useState(true);

  return (
    <ThemedView
      className="px-6 py-8 mb-6 mr-4 bg-gray-800 rounded-xl dark:bg-zinc-700"
      style={{ 
        width: width * 0.85,
        paddingHorizontal: 12,
      }}
    >
        <ThemedView style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text style={{
            fontSize: 16,
            color: '#ffffffcc',
            fontWeight: '500',
          }}>{account.label}</Text>

          {/* Toggle balance visibility icon */}
          <TouchableOpacity onPress={() => setShowBalance(!showBalance)}>
            {showBalance ? (
              <Eye size={24} color="#ffffff99" />
            ) : (
              <EyeOff size={24} color="#ffffff99" />
            )}
          </TouchableOpacity>
        </ThemedView>

        {/* Balance amount */}
        <Text style={{
          fontSize: 32,
          fontWeight: 'bold',
          color: '#fff',
          marginTop: 8,
        }}>
          {showBalance ? account.balance : '••••••••'}
        </Text>

        {/* Link to Transactions */}
        <ThemedLink href={`/dashboard/transactions/${account.id}`} className="mt-4">
          <ThemedText style={{ color: '#f9f9f9' }}>View Transactions</ThemedText>
        </ThemedLink>
      </ThemedView>
  );
};

export default BalanceCard