import { color } from '@/constants/theme';
import { BottomSheetTextInput } from '@gorhom/bottom-sheet';
import { Eye, EyeOff } from 'lucide-react-native';
import { useColorScheme } from 'nativewind';
import React, { useEffect, useState } from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';

interface ThemedTextInputProps {
  className?: string;
  useBottomSheetTextInput?: boolean;
  togglePasswordVisibility?: boolean;
  [x: string]: any; // For other props
}

const ThemedTextInput = ({ className, useBottomSheetTextInput = false, togglePasswordVisibility = false, ...props }: ThemedTextInputProps) => {
  const { colorScheme } = useColorScheme();
  const theme = colorScheme === 'dark' ? 'dark' : 'light';

  const [showPassword, setShowPassword] = useState(false);

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  useEffect(() => {
    setShowPassword(props.secureTextEntry || false);
  }, [props.secureTextEntry]);

  const TextInputComponent = useBottomSheetTextInput ? BottomSheetTextInput : TextInput;

  const TheTextInput = (
    <TextInputComponent
      {...props}
      placeholderTextColor={theme === 'dark' ? color.dark.placeholderTextColor : color.light.placeholderTextColor}
      className={`
        dark:text-white text-black
        bg-white dark:bg-neutral-800 
        rounded-xl
        ${className}
      `}
      style={{
        paddingHorizontal: 12,
        paddingVertical: 16,
        marginVertical: 12,
        borderWidth: 1,
        borderColor: theme === 'dark' ? '#171717' : '#d1d5db',
      }}
      secureTextEntry={showPassword}
      keyboardType={props.keyboardType || "default"}
      importantForAutofill={showPassword ? "no" : "auto"}
      autoComplete={props.autoComplete || "off"}
      autoCapitalize={props.autoCapitalize || "none"}
      autoCorrect={props.autoCorrect || false}
    />
  );

  return togglePasswordVisibility ? (
    <View className="relative">
      {TheTextInput}
      <TouchableOpacity
        className="absolute right-4 top-1/2 transform -translate-y-1/2"
        onPress={handleTogglePasswordVisibility}
      >
        <Text>
          {showPassword 
            ? <EyeOff size={24} color={theme === 'dark' ? color.dark.placeholderTextColor : color.light.placeholderTextColor} /> 
            : <Eye size={24} color={theme === 'dark' ? color.dark.placeholderTextColor : color.light.placeholderTextColor} />
          }
        </Text>
      </TouchableOpacity>
    </View>
  ) : (
    TheTextInput
  );
}

export default ThemedTextInput;