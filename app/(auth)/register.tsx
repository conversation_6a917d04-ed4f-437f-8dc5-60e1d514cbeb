import ThemedButton from '@/components/ThemedButton';
import ThemedContainer from '@/components/ThemedContainer';
import ThemedDropdownSelect from '@/components/ThemedDropdownSelect';
import ThemedLink from '@/components/ThemedLink';
import ThemedText from '@/components/ThemedText';
import ThemedTextInput from '@/components/ThemedTextInput';
import ThemedView from '@/components/ThemedView';
import { useTheme } from '@/context/ThemeContext';
import { getCalendars, getLocales } from 'expo-localization';
import { useRouter } from 'expo-router';
// import { PhoneInput, PhoneInputProps } from 'react-native-phone-entry';
import { register } from '@/api/auth';
import { getCountries } from '@/api/utils';
import FormInputErrorMessage from '@/components/FormInputErrorMessage';
import useAuthStore from '@/stores/useAuthStore';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Image } from 'react-native';
import { toast } from 'sonner-native';
import * as yup from 'yup';

const RegisterScreen = () => {
  const router = useRouter();

  const { theme, setTheme } = useTheme();
  const authStore = useAuthStore((state) => state);
  const { timeZone } = getCalendars()[0];
  const { regionCode } = getLocales()[0];

  const [isLoading, setIsLoading] = useState(false);

  const [countries, setCountries] = useState<any>([]);
  const [country, setCountry] = useState<any>(null);
  const [countryOptionValue, setCountryOptionValue] = useState<any>(null);
  const [defaultCountryOptionValue, setDefaultCountryOptionValue] = useState<any>(null);

  const howDidYouHearAboutUs = [
    { label: 'Google', value: 'Google', },
    { label: 'Facebook', value: 'Facebook', },
    { label: 'Instagram', value: 'Instagram', },
    { label: 'X (Twitter)', value: 'X', },
    { label: 'LinkedIn', value: 'LinkedIn', },
    { label: 'Referral', value: 'Referral', },
    { label: 'I am an existing user', value: 'I_Am_An_Existing_User', },
    { label: 'Others', value: 'Others', },
  ];  

  // Define a validation schema
    const schema = yup.object({
      first_name: yup.string().required().label('First name'),
      last_name: yup.string().required().label('Last name'),
      business_name: yup.string().required().label('Business name'),
      email: yup.string().email().required().label('Business email'),
      phone_number: yup.string().required().label('Phone number'),
      phone_number_country: yup.string().required().label('Country code'),
      password: yup.string().required().label('Password'),
      password_confirmation: yup.string()
                              .required()
                              .label('Confirm password')
                              .oneOf([yup.ref('password')], 'Password confirmation did not match'),
      how_did_you_hear_about_us: yup.string().required().label('How did you hear about us'),
      referral_code: yup.string().label('Referral code'),
    });
  
    const { control, handleSubmit, formState: { errors }, reset, setError } = useForm({
      defaultValues: {
        first_name: "",
        last_name: "",
        business_name: "",
        email: "",
        phone_number: "",
        phone_number_country: "",
        password: "",
        password_confirmation: "",
        how_did_you_hear_about_us: "",
        referral_code: "",
      },
      resolver: yupResolver(schema),
    })
  
    const onSubmit = (data: any) => {
      data = {
        ...data,
        type: 'business',
        country: data.phone_number_country,
      };
      setIsLoading(true);
      register(data).then((response) => {
        if (response.status === "success") {
          // store user data
          authStore.setUserAuthToken(response.data);
          // Set last activity to skip lock screen
          authStore.setLastActivity();
          // clear form
          reset();
          setIsLoading(false);
          router.replace('/dashboard');
        } else {
          if (response.errors) {
            Object.keys(response.errors).forEach((key: any) => {
              setError(key, {
                type: 'custom',
                message: response.errors[key][0],
              });
            });
          }
          toast.error(response.message);
          setIsLoading(false);
        }
      });
    };
  

  const fetchCountries = async () => {
    setIsLoading(true);
    await getCountries().then((response) => {
      if (response.status === "success") {
        const supportedCountries: any = ['NG', '*'];
        let countryList = response.data;        
        // filter only support countries
        countryList = countryList.filter((country: any) => {
          return !supportedCountries 
            || supportedCountries.length <= 0 
            || supportedCountries === '*' 
            || supportedCountries.includes('*')
            || supportedCountries.includes(country.code);
        });
        // add custom field
        countryList = countryList.map((country: any) => {
          const customName = '+' + country.calling_code + ' (' + country.name + ')';
          // const flag = `https://flagcdn.com/${country.code?.toLowerCase()}.svg`; // SVG is not supported
          const flag = `https://flagcdn.com/w80/${country.code?.toLowerCase()}.png`;

          const CustomLabel = (
            <ThemedView
              style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
            >
              <Image
                style={{
                  height: 12,
                  width: 16,
                  // borderRadius: 20,
                  borderRadius: 2,
                  marginRight: 5,
                }}
                source={{
                  uri: flag,
                }}
              />
              <ThemedText>{customName}</ThemedText>
            </ThemedView>
          );
          
          return {
            ...country,
            // value: country.code,
            // using a combination of code, name and dial code as the value to make search works since the label is a component
            value: country.code + '|' + country.name + '|' + country.calling_code, 
            label: CustomLabel,
          };
        });
        
        // set default country option value
        const defaultCountry = countryList.find((country: any) => country.code?.toUpperCase() === regionCode?.toUpperCase());
        if (defaultCountry) {
          setDefaultCountryOptionValue(defaultCountry.value);
        }
        // set countries
        setCountries(countryList);
      } else {
        toast.error(response.message);
      }
      setIsLoading(false);
    });
  };

  useEffect(() => {
    // fetch countries on mount
    fetchCountries();
  }, []);

  return (
    <ThemedContainer
      className=""
      scrollableView={true}
      loading={isLoading}
      containerFooter={
        <ThemedButton onPress={handleSubmit(onSubmit)}>Create Account</ThemedButton>
      }
    >
      <ThemedText className="mb-3 text-2xl font-semibold">Create a business account</ThemedText>
      <ThemedText className="mb-8">
        Already have an account? <ThemedLink href="/login" className="font-semibold">Login</ThemedLink>
      </ThemedText>

      <ThemedView className="">

        <Controller
          control={control}
          rules={{
            required: true,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <ThemedTextInput
              placeholder="First Name"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
            />
            )}
          name="first_name"
        />
        {errors.first_name && <FormInputErrorMessage>{ errors.first_name.message }</FormInputErrorMessage>}

        <Controller
          control={control}
          rules={{
            required: true,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <ThemedTextInput
              placeholder="Last Name (Surname)"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
            />
            )}
          name="last_name"
        />
        {errors.last_name && <FormInputErrorMessage>{ errors.last_name.message }</FormInputErrorMessage>}
        
        <Controller
          control={control}
          rules={{
            required: true,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <ThemedTextInput
              placeholder="Business Name"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
            />
            )}
          name="business_name"
        />
        {errors.business_name && <FormInputErrorMessage>{ errors.business_name.message }</FormInputErrorMessage>}

        <Controller
          control={control}
          rules={{
            required: true,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <ThemedTextInput
              placeholder="Business Email"
              keyboardType="email-address"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
            />
            )}
          name="email"
        />
        {errors.email && <FormInputErrorMessage>{ errors.email.message }</FormInputErrorMessage>}

        <ThemedView className="flex-row items-center overflow-hidden">
          <ThemedView className="w-[110px] pr-3">
            <Controller
              control={control}
              rules={{
                required: true,
              }}
              render={({ field: { onChange, onBlur, value } }) => (
                <ThemedDropdownSelect
                  label=""
                  placeholder="Select..."
                  options={countries}
                  optionLabel={'label'}
                  optionValue={'value'}
                  selectedValue={countryOptionValue || defaultCountryOptionValue}
                  onValueChange={(value) => {
                    setCountryOptionValue(value);
                    if (value) {
                      const countryData = value.split('|');
                      setCountry(countryData[0]);
                    }
                  }}
                  isSearchable={true}
                  error={country === undefined ? 'Country code is required' : ''}
                />
                )}
              name="phone_number_country"
            />
          </ThemedView>
          <Controller
            control={control}
            rules={{
              required: true,
            }}
            render={({ field: { onChange, onBlur, value } }) => (
              <ThemedTextInput
                placeholder="Phone Number"
                keyboardType="phone-pad"
                className="flex-1"
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
              />
              )}
            name="phone_number"
          />
        </ThemedView>
        {(errors.phone_number || errors.phone_number_country) && <FormInputErrorMessage>{ errors.phone_number?.message || errors.phone_number_country?.message }</FormInputErrorMessage>}

        <Controller
          control={control}
          rules={{
            required: true,
            minLength: 6,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <ThemedTextInput
              placeholder="Password"
              secureTextEntry
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              togglePasswordVisibility={true}
            />
            )}
          name="password"
        />
        {errors.password && <FormInputErrorMessage>{ errors.password.message }</FormInputErrorMessage>}

        <Controller
          control={control}
          rules={{
            required: true,
            minLength: 6,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <ThemedTextInput
              placeholder="Confirm Password"
              secureTextEntry
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              togglePasswordVisibility={true}
            />
            )}
          name="password_confirmation"
        />
        {errors.password_confirmation && <FormInputErrorMessage>{ errors.password_confirmation.message }</FormInputErrorMessage>}

        <Controller
          control={control}
          rules={{
            required: true,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <ThemedDropdownSelect
              label=""
              placeholder="How did you hear about us?"
              options={howDidYouHearAboutUs}
              optionLabel={'label'}
              optionValue={'value'}
              selectedValue={value}
              onValueChange={onChange}
            />
            )}
          name="how_did_you_hear_about_us"
        />
        {errors.how_did_you_hear_about_us && <FormInputErrorMessage>{ errors.how_did_you_hear_about_us.message }</FormInputErrorMessage>}

        <Controller
          control={control}
          render={({ field: { onChange, onBlur, value } }) => (
            <ThemedTextInput
              placeholder="Referral Code (Optional)"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}

            />
            )}
          name="referral_code"
        />
        {errors.referral_code && <FormInputErrorMessage>{ errors.referral_code.message }</FormInputErrorMessage>}

        <ThemedText className="mt-6 text-center">
          By signing up, you agree to our <ThemedLink href="/terms" className="font-semibold">Terms of Service</ThemedLink> and <ThemedLink href="/privacy" className="font-semibold">Privacy Policy</ThemedLink>
        </ThemedText>
      
      </ThemedView>
    </ThemedContainer>

  );
};

export default RegisterScreen;