import { login } from '@/api/auth';
import FormInputErrorMessage from '@/components/FormInputErrorMessage';
import OtpVerificationModal from '@/components/OtpVerificationModal';
import ThemedButton from '@/components/ThemedButton';
import ThemedContainer from '@/components/ThemedContainer';
import ThemedLink from '@/components/ThemedLink';
import ThemedText from '@/components/ThemedText';
import ThemedTextInput from '@/components/ThemedTextInput';
import ThemedView from '@/components/ThemedView';
import useAuthStore from '@/stores/useAuthStore';
import { yupResolver } from '@hookform/resolvers/yup';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'sonner-native';
import * as yup from 'yup';

const LoginScreen = () => {
  const router = useRouter();
  const authStore = useAuthStore((state) => state)

  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState<any>(null);
  const [verificationTokenOptions, setVerificationTokenOptions] = useState<any>(null);
  const [isOtpVerificationModalVisible, setIsOtpVerificationModalVisible] = useState(false);
    
  // Define a validation schema
  const schema = yup.object({
    email: yup.string().email().required().label('Email address'),
    password: yup.string().required().label('Password'),
  });

  const { control, handleSubmit, formState: { errors }, reset, setError } = useForm({
    defaultValues: {
      // email: "",
      // password: "",
      email: "<EMAIL>",
      password: "sysadmin",
    },
    resolver: yupResolver(schema),
  })

  const onSubmit = (data: any) => {
    setFormData(data);
    setIsLoading(true);
    login(data.email, data.password, data.verification_token_signature || null).then((response) => {
      if (response.status === "success") {
        // check if verification token is required
        if (response.data?.require_verification_token_signature) {
          const verificationTokenOptions = {
            ...response.data.verification_token_options,
            // email: values.email,
          };
          setIsLoading(false);
          // open verification modal
          setIsOtpVerificationModalVisible(true);
          setVerificationTokenOptions(verificationTokenOptions);
        } else {
          // store user data
          authStore.setUserAuthToken(response.data);
          // Set last activity to skip lock screen
          authStore.setLastActivity();
          // clear form
          reset();
          setFormData(null);
          setIsLoading(false);
          router.replace('/dashboard');
        }  
      } else {
        if (response.errors) {
          Object.keys(response.errors).forEach((key: any) => {
            setError(key, {
              type: 'custom',
              message: response.errors[key][0],
            });
          });
        }
        toast.error(response.message);
        setIsLoading(false);
      }
    });
  };

  const handleOtpVerificationConfirm = (data: any) => {
    setIsOtpVerificationModalVisible(false);
    // resubmit form with verification token signature
    onSubmit({
      ...formData,
      verification_token_signature: data.verification_token_signature,
    });
  };

  return (
    <ThemedContainer
      scrollableView={true} 
      // className="justify-center"   
      loading={isLoading}   
      containerFooter={
        <ThemedButton onPress={handleSubmit(onSubmit)}>Sign In</ThemedButton>
      }
    >      
      <ThemedText className="mb-3 text-2xl font-semibold">Welcome Back</ThemedText>
      <ThemedText className="mb-8">
        Don&apos;t have an account? &nbsp;
        <ThemedLink href="/register" className="font-semibold">Create Account</ThemedLink>
      </ThemedText>

      <ThemedView className="">
        <Controller
          control={control}
          rules={{
            required: true,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <ThemedTextInput
              placeholder="Email"
              keyboardType="email-address"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
            />
            )}
          name="email"
        />
        {errors.email && <FormInputErrorMessage>{ errors.email.message }</FormInputErrorMessage>}
        
        <Controller
          control={control}
          rules={{
            required: true,
            minLength: 6,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <ThemedTextInput
              placeholder="Password"
              secureTextEntry
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              togglePasswordVisibility={true}
            />
            )}
          name="password"
        />
        {errors.password && <FormInputErrorMessage>{ errors.password.message }</FormInputErrorMessage>}
        
        <ThemedLink href="/reset-password" className="self-start mt-1 font-semibold">Forgot password?</ThemedLink>
        
      </ThemedView>
      
      <OtpVerificationModal
        visible={isOtpVerificationModalVisible}
        setVisible={setIsOtpVerificationModalVisible}
        verificationTokenOptions={verificationTokenOptions}
        onDismiss={() => setIsOtpVerificationModalVisible(false)}
        onConfirm={handleOtpVerificationConfirm}
        secureTextEntry={false}
      />
    </ThemedContainer>
  );
};

export default LoginScreen;