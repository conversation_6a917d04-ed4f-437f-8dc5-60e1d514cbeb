import { color } from '@/constants/theme';
import { Tabs } from 'expo-router';
import { CircleUser, House, Landmark, Send } from 'lucide-react-native';
import { useColorScheme } from 'nativewind';
import React from 'react';

const TabLayout = () => {
  const { colorScheme } = useColorScheme();
  const theme = colorScheme === 'dark' ? 'dark' : 'light';

  return (
    <Tabs
      screenOptions={{
        tabBarStyle: {
          backgroundColor: theme === 'dark' ? color.dark.backgroundColor : color.light.backgroundColor,
          borderTopColor: theme === 'dark' ? color.dark.placeholderTextColor : color.light.placeholderTextColor,
          borderTopWidth: 0.3,
          paddingTop: 10,
          paddingBottom: 20,
          height: 85,
        },
        tabBarActiveTintColor: theme === 'dark' ? color.dark.textColor : color.light.textColor,
        // tabBarInactiveTintColor: theme === 'dark' ? color.dark.textColor : color.light.textColor,
      }}
    >
      <Tabs.Screen
        name="index"
        options={{ 
          headerShown: false,
          title: 'Home',
          tabBarLabel: 'Home',
          tabBarIcon: ({ color }) => <House size={28}  color={color} />,
          // tabBarActiveTintColor: theme === 'dark' ? color.dark.textColor : color.light.textColor,
        }}        
      />
      <Tabs.Screen
        name="accounts"
        options={{ 
          headerShown: false,
          title: 'Accounts',
          tabBarLabel: 'Accounts',
          tabBarIcon: ({ color }) => <Landmark size={28} color={color} />,
        }}
      />
      <Tabs.Screen
        name="pay"
        options={{ 
          headerShown: false,
          title: 'Pay',
          tabBarLabel: 'Pay',
          tabBarIcon: ({ color }) => <Send size={28} color={color} />,
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{ 
          headerShown: false,
          title: 'More',
          tabBarLabel: 'More',
          tabBarIcon: ({ color }) => <CircleUser size={28} color={color} />,
        }}
      />
    </Tabs>
    
  )
};

export default TabLayout;