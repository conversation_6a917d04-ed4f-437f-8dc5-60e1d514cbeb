import BalanceCard from '@/components/BalanceCard';
import ThemedContainer from '@/components/ThemedContainer';
import ThemedTabHeader from '@/components/ThemedTabHeader';
import ThemedText from '@/components/ThemedText';
import { color } from '@/constants/theme';
import useAuthStore from '@/stores/useAuthStore';
import { useRouter } from 'expo-router';
import { Bell, MessagesSquare } from 'lucide-react-native';
import { useColorScheme } from 'nativewind';
import { useEffect, useState } from 'react';
import { Dimensions, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { KeyboardAwareFlatList } from 'react-native-keyboard-aware-scroll-view';

// const Logo = require('@/assets/images/logo.png');
// const LogoDark = require('@/assets/images/logo-dark.png');
const LogoIcon = require('@/assets/images/favicon.png')

const accounts = [
  {
    id: '1',
    label: 'Main',
    balance: 245500.00,
  },
  {
    id: '2',
    label: 'Savings',
    balance: 100000.00,
  },
  {
    id: '3',
    label: 'Fixed Deposit',
    balance: 500000.00,
  },
];



const HomeScreen = () => {
  const router = useRouter();
  const authStore = useAuthStore((state) => state);

  const { colorScheme } = useColorScheme();
  const theme = colorScheme === 'dark' ? 'dark' : 'light';
  const width = Dimensions.get('window').width;

  const [showBalance, setShowBalance] = useState(true);

  const hello = async () => {
    // 
  }

  useEffect(() => {
    // Set last activity to skip lock screen
    // const authStore = useAuthStore((state) => state);
    // authStore.setLastActivity();
    hello();
  }, []);
  
  
  return (
    <ThemedContainer
      scrollableView={true}
      viewIsInsideTabBar={true}
      className="pt-20"
      containerHeader={
        <ThemedTabHeader
          leftComponent={
            <>
              <Image 
                // source={{ uri: userPhoto }} 
                source={ LogoIcon } 
                className="!w-12 !h-12 border rounded-full bg-neutral-200 dark:bg-neutral-700 border-neutral-300 dark:border-neutral-600"
              />
              <ThemedText className="ml-2 text-lg font-semibold">Hi, {authStore.user?.first_name}</ThemedText>
            </>
          }
          rightComponent={
            <>
              <TouchableOpacity onPress={() => console.log('Notifications')} className="p-1.5">
                <Bell size={24} color={theme === 'dark' ? color.dark.textColor : color.light.textColor} />
              </TouchableOpacity>
              <TouchableOpacity onPress={() => console.log('Support')} className="p-1.5">
                <MessagesSquare size={24} color={theme === 'dark' ? color.dark.textColor : color.light.textColor} />
              </TouchableOpacity>
            </>
          }
        />
      }
    >
      {/* Balance Card */}
      
      <KeyboardAwareFlatList
        horizontal={true}        
        pagingEnabled
        snapToAlignment="start"
        snapToInterval={width * 0.85} // width of each item
        decelerationRate="fast"
        keyboardShouldPersistTaps="handled"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 0 }}
        data={accounts}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <BalanceCard account={item} />
        )}
      />

      {/* Quick Actions */}
      <View style={styles.actionsRow}>
        <ActionButton label="Send" />
        <ActionButton label="Top Up" />
        <ActionButton label="Withdraw" />
      </View>

      {/* <ThemedText className="mb-3 text-lg font-semibold">Recent Transactions</ThemedText> */}

        

    </ThemedContainer>
  );
};

export default HomeScreen;


const ActionButton = ({ label }: { label: string }) => (
  <TouchableOpacity style={styles.actionButton}>
    <Text style={styles.actionLabel}>{label}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f9fc',
  },
  balanceLabel: {
    color: '#a0a0a0',
    fontSize: 16,
  },
  balanceAmount: {
    color: '#fff',
    fontSize: 32,
    fontWeight: 'bold',
    marginTop: 4,
  },
  actionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  actionButton: {
    backgroundColor: '#ffffff',
    paddingVertical: 12,
    paddingHorizontal: 18,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  actionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e1e2d',
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 1,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  transactionDate: {
    fontSize: 13,
    color: '#999',
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },

  // container: {
  //   flex: 1,
  //   backgroundColor: '#f9f9f9',
  // },
  content: {
    padding: 20,
  },
  welcome: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 20,
  }, 
});