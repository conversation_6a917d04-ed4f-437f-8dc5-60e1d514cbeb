{"name": "payinto-business", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.5.2", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.81.2", "axios": "^1.10.0", "buffer": "^6.0.3", "crypto-es": "^2.1.0", "crypto-js": "^4.2.0", "expo": "53.0.16", "expo-application": "~6.1.5", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.6", "expo-localization": "~16.1.6", "expo-router": "~5.1.2", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "^14.2.0", "lucide-react-native": "^0.525.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.58.1", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-input-select": "^2.1.7", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-otp-entry": "^1.8.5", "react-native-phone-entry": "^0.2.0", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "sonner": "^2.0.5", "sonner-native": "^0.21.0", "tailwindcss": "^3.4.17", "uuid": "^11.1.0", "yup": "^1.6.1", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.27.7", "@types/react": "~19.0.10", "eslint": "^9.29.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}